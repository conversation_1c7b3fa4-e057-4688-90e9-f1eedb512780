'use client';
import {
  ChevronDownIcon,
  ChevronUpIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';
import classNames from 'classnames';
import { ReactNode, useCallback, useMemo, useState } from 'react';
import { twMerge } from 'tailwind-merge';

type Props = {
  placeholder?: string | ReactNode;
  className?: string;
  bodyClassName?: string;
  options?: {
    id: number | string;
    name: string;
  }[];
  value?: (string | number)[];
  onChange?: (value: { id: string | number; name: string }) => void;
  isClearable?: boolean;
  onRemove?: (id: string | number) => void;
};

const MultiSelect = ({
  className = '',
  bodyClassName = '',
  options = [],
  placeholder = '',
  value,
  onChange,
  isClearable,
  onRemove,
}: Props) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const selected = useMemo(
    () => options?.filter((_o) => value?.includes(_o.id)) ?? [],
    [options, value]
  );

  const onRemoveItem = useCallback(
    (e: any, id: any) => {
      e.preventDefault();
      e.stopPropagation();
      onRemove?.(id);
    },
    [onRemove]
  );

  return (
    <>
      <div
        className={classNames(
          twMerge(
            'relative px-4 py-2 border rounded-md flex items-center justify-between cursor-pointer',
            className
          )
        )}
        onClick={() => setIsOpen(!isOpen)}
      >
        {selected.length > 0 ? (
          <div className="w-[90%] flex flex-wrap gap-2">
            <div className="border px-2 rounded cursor-default flex items-center">
              {selected[0].name}
              {isClearable && (
                <XCircleIcon
                  onClick={(e) => onRemoveItem(e, selected[0].id)}
                  className="ml-2 w-5 h-5 cursor-pointer"
                />
              )}
            </div>
            {selected.length > 1 && (
              <div className="border px-2 rounded cursor-default flex items-center">
                +{selected.length - 1}
              </div>
            )}
          </div>
        ) : (
          <>
            {typeof placeholder === 'string' && (
              <p className="w-[90%]">{placeholder}</p>
            )}
            {typeof placeholder === 'object' && placeholder}
          </>
        )}
        {isOpen ? (
          <ChevronUpIcon className="w-auto h-[14px]" />
        ) : (
          <ChevronDownIcon className="w-auto h-[14px]" />
        )}
        {isOpen && (
          <>
            <div
              className={classNames(
                twMerge(
                  'absolute left-0 right-0 top-[40px] bg-white shadow-card py-2 rounded-md z-[999]',
                  bodyClassName
                )
              )}
            >
              {options?.map((_option, index) => (
                <div
                  key={index}
                  onClick={() => onChange?.(_option)}
                  className={classNames(
                    'hover:bg-gray-100 py-2 pl-4 text-left font-normal',
                    {
                      'text-carolina-blue font-medium': value?.includes(
                        _option.id
                      ),
                    }
                  )}
                >
                  {_option.name}
                </div>
              ))}
            </div>
          </>
        )}
      </div>
      {isOpen && (
        <div
          className="drawer-overlay fixed inset-0 z-[99]"
          onClick={() => setIsOpen(!isOpen)}
        />
      )}
    </>
  );
};

export default MultiSelect;
