'use client';
import React, { useState, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { Property } from '@/types/property';
import {
  SortConfig,
  Column,
  PropertiesTableBaseProps,
  DEFAULT_PROPS,
} from './types';

export type PropertiesTableClientProps = PropertiesTableBaseProps & {
  staticTable: ReactNode;
  columns: Column[];
};

/**
 * Client component for Properties Table
 * Handles all interactive functionality: selection, sorting, pagination
 */
export const PropertiesTableClient = ({
  data = DEFAULT_PROPS.data,
  total = DEFAULT_PROPS.total,
  flag: _flag = DEFAULT_PROPS.flag,
  offset = DEFAULT_PROPS.offset,
  currentPage = DEFAULT_PROPS.currentPage,
  searchInfor: _searchInfor = DEFAULT_PROPS.searchInfor,
  limit = DEFAULT_PROPS.limit,
  staticTable,
  columns,
  onPagesChanged,
  onSort,
  onSubset,
  onShowAllPage,
  onLoad,
}: PropertiesTableClientProps) => {
  const router = useRouter();

  // State
  const [selectedItems, setSelectedItems] = useState<Property[]>([]);
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    key: null,
    direction: 'asc',
  });
  const [showActions, setShowActions] = useState(false);
  const [loading] = useState(false);

  // Utility functions
  const truncate = (str: string, length: number) => {
    return str.length > length ? str.substring(0, length) + '...' : str;
  };

  // Event handlers
  const handleSelectAll = () => {
    if (selectedItems.length === data.length) {
      setSelectedItems([]);
      setShowActions(false);
    } else {
      setSelectedItems(data);
      setShowActions(true);
    }
  };

  const handleSelectItem = (property: Property) => {
    const isSelected = selectedItems.find(
      (item) => item.listing_id === property.listing_id
    );

    if (isSelected) {
      const newSelected = selectedItems.filter(
        (item) => item.listing_id !== property.listing_id
      );
      setSelectedItems(newSelected);
      setShowActions(newSelected.length > 0);
    } else {
      const newSelected = [...selectedItems, property];
      setSelectedItems(newSelected);
      setShowActions(true);
    }
  };

  const handleSort = (key: string) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
    onSort?.({ prop: key, order: direction });
  };

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= Math.ceil(total / limit)) {
      onPagesChanged?.(page);
    }
  };

  const handleRowClick = (property: Property) => {
    router.push(`/rental-listings/${property.listing_id}/overview`);
  };

  const handleSubset = () => {
    onSubset?.(selectedItems);
    setSelectedItems([]);
    setShowActions(false);
  };

  const handleLoad = () => {
    onLoad?.();
  };

  // Render interactive table with client-side functionality
  const interactiveTable = (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            {columns.map((column) => (
              <th
                key={column.key}
                className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${column.width}`}
              >
                {column.key === 'checkbox' ? (
                  <input
                    type="checkbox"
                    checked={
                      selectedItems.length === data.length && data.length > 0
                    }
                    onChange={handleSelectAll}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                ) : column.key === 'actions' ? (
                  <span className="sr-only">Actions</span>
                ) : (
                  <button
                    onClick={() => column.sortable && handleSort(column.key)}
                    className={`flex items-center space-x-1 ${
                      column.sortable
                        ? 'hover:text-gray-700 cursor-pointer'
                        : ''
                    }`}
                    disabled={!column.sortable}
                  >
                    <span>{column.label}</span>
                    {column.sortable && sortConfig.key === column.key && (
                      <span className="text-blue-600">
                        {sortConfig.direction === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </button>
                )}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {data.map((property) => (
            <tr
              key={property.listing_id}
              className={`hover:bg-gray-50 cursor-pointer ${
                selectedItems.find(
                  (item) => item.listing_id === property.listing_id
                )
                  ? 'bg-blue-50'
                  : ''
              }`}
            >
              <td className="px-6 py-4 whitespace-nowrap">
                <input
                  type="checkbox"
                  checked={
                    !!selectedItems.find(
                      (item) => item.listing_id === property.listing_id
                    )
                  }
                  onChange={() => handleSelectItem(property)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </td>
              <td
                className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                onClick={() => handleRowClick(property)}
              >
                {property.listing_id}
              </td>
              <td
                className="px-6 py-4 whitespace-nowrap"
                onClick={() => handleRowClick(property)}
              >
                <div className="text-sm text-gray-900">
                  {truncate(property.address || 'N/A', 40)}
                </div>
                {property.area && (
                  <div className="text-xs text-gray-500">
                    {property.area.name}
                  </div>
                )}
              </td>
              <td
                className="px-6 py-4 whitespace-nowrap"
                onClick={() => handleRowClick(property)}
              >
                <div className="text-sm text-gray-900">
                  {property.homeowner?.name || 'N/A'}
                </div>
                {property.homeowner?.email1 && (
                  <div className="text-xs text-gray-500">
                    {property.homeowner.email1}
                  </div>
                )}
              </td>
              <td
                className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                onClick={() => handleRowClick(property)}
              >
                {property.nr_listing_id || 'N/A'}
              </td>
              <td
                className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                onClick={() => handleRowClick(property)}
              >
                {property.key_number || 'N/A'}
              </td>
              <td
                className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                onClick={() => handleRowClick(property)}
              >
                {property.bedroom_number}
              </td>
              <td
                className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                onClick={() => handleRowClick(property)}
              >
                {property.capacity}
              </td>
              <td
                className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                onClick={() => handleRowClick(property)}
              >
                ${property.min_price?.toLocaleString() || 'N/A'}
              </td>
              <td
                className="px-6 py-4 whitespace-nowrap"
                onClick={() => handleRowClick(property)}
              >
                <span
                  className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    property.publish
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}
                >
                  {property.publish ? 'Published' : 'Draft'}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div className="flex space-x-2">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRowClick(property);
                    }}
                    className="text-blue-600 hover:text-blue-900"
                  >
                    View
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      router.push(
                        `/rental-listings/${property.listing_id}/edit`
                      );
                    }}
                    className="text-blue-600 hover:text-blue-900"
                  >
                    Edit
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  return (
    <div className="bg-white shadow-sm rounded-lg">
      {/* Actions Bar */}
      {showActions && (
        <div className="bg-blue-50 px-4 py-3 border-b border-blue-200">
          <div className="flex items-center justify-between">
            <p className="text-sm text-blue-700">
              {selectedItems.length} item{selectedItems.length !== 1 ? 's' : ''}{' '}
              selected
            </p>
            <div className="flex space-x-2">
              <button
                onClick={handleSubset}
                className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
              >
                Create Subset
              </button>
              <button
                onClick={handleLoad}
                className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors"
              >
                Load Selected
              </button>
              <button
                onClick={() => {
                  setSelectedItems([]);
                  setShowActions(false);
                }}
                className="px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700 transition-colors"
              >
                Clear Selection
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Table */}
      {interactiveTable}

      {/* Pagination */}
      {data.length > 0 && (
        <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">{offset + 1}</span> to{' '}
                <span className="font-medium">
                  {Math.min(offset + limit, total)}
                </span>{' '}
                of <span className="font-medium">{total}</span> results
              </p>
              <button
                onClick={onShowAllPage}
                className="ml-4 text-sm text-blue-600 hover:text-blue-800 transition-colors"
              >
                Show all on one page
              </button>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <span className="text-sm text-gray-700">
                Page {currentPage} of {Math.ceil(total / limit)}
              </span>
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage >= Math.ceil(total / limit)}
                className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
          <div className="text-gray-500">Loading...</div>
        </div>
      )}
    </div>
  );
};
