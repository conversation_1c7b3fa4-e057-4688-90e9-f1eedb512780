# Properties Table Components

This directory contains the Properties Table components separated into server and client components for better performance and maintainability.

## Components

### PropertiesTable.tsx (Wrapper)
- **Type**: Client Component (for backward compatibility)
- **Purpose**: Maintains the existing API while using the new separated components
- **Usage**: Drop-in replacement for the original PropertiesTable

### PropertiesTableServer.tsx
- **Type**: Server Component
- **Purpose**: Handles static rendering and data preparation
- **Features**:
  - Static table structure generation
  - Data preprocessing
  - SEO-friendly server-side rendering
  - No JavaScript required for basic display

### PropertiesTableClient.tsx
- **Type**: Client Component
- **Purpose**: Handles all interactive functionality
- **Features**:
  - Row selection and bulk actions
  - Sorting functionality
  - Pagination controls
  - Navigation and routing
  - State management

## Architecture Benefits

### Performance
- **Server Component**: Renders static content on the server, reducing client-side JavaScript
- **Client Component**: Only handles interactive features, minimizing hydration overhead
- **Selective Hydration**: Only interactive parts require JavaScript

### SEO & Accessibility
- **Server-side Rendering**: Table content is available immediately for search engines
- **Progressive Enhancement**: Works without JavaScript, enhanced with interactivity
- **Better Core Web Vitals**: Faster initial page load and reduced CLS

### Maintainability
- **Separation of Concerns**: Static rendering vs. interactive logic
- **Reusable Components**: Server component can be used independently
- **Type Safety**: Shared types ensure consistency across components

## Usage Examples

### Basic Usage (Backward Compatible)
```tsx
import PropertiesTable from './PropertiesTable';

<PropertiesTable
  data={properties}
  total={totalCount}
  currentPage={page}
  onPagesChanged={handlePageChange}
  onSort={handleSort}
/>
```

### Server Component Only (Static)
```tsx
import { PropertiesTableServer } from './PropertiesTableServer';

<PropertiesTableServer
  data={properties}
  total={totalCount}
  currentPage={page}
/>
```

### Client Component with Custom Static Table
```tsx
import { PropertiesTableClient } from './PropertiesTableClient';

<PropertiesTableClient
  data={properties}
  staticTable={customStaticTable}
  columns={customColumns}
  onPagesChanged={handlePageChange}
/>
```

## File Structure

```
src/clients/views/properties/
├── PropertiesTable.tsx          # Backward compatibility wrapper
├── PropertiesTableServer.tsx    # Server component
├── PropertiesTableClient.tsx    # Client component
├── types.ts                     # Shared types and constants
├── index.ts                     # Exports
└── README.md                    # This file
```

## Migration Guide

### For Existing Code
No changes required - the original `PropertiesTable` component maintains full backward compatibility.

### For New Code
Consider using the separated components directly:
- Use `PropertiesTableServer` for static displays
- Use `PropertiesTableClient` when you need full interactivity
- Use the wrapper `PropertiesTable` for quick implementation

## Types

### PropertiesTableBaseProps
Base properties shared across all components.

### SortConfig
Configuration for table sorting state.

### Column
Table column definition with sorting and styling options.

## Constants

### DEFAULT_COLUMNS
Standard column configuration for properties table.

### DEFAULT_PROPS
Default values for component props.
