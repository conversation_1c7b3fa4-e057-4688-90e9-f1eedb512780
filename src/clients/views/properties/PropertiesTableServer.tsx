import React from 'react';
import { PropertiesTableClient } from './PropertiesTableClient';
import {
  PropertiesTableBaseProps,
  DEFAULT_COLUMNS,
  DEFAULT_PROPS,
} from './types';

export type PropertiesTableServerProps = Omit<
  PropertiesTableBaseProps,
  'onPagesChanged' | 'onSort' | 'onSubset' | 'onShowAllPage' | 'onLoad'
>;

/**
 * Server component for Properties Table
 * Handles static rendering and data preparation
 */
export const PropertiesTableServer = ({
  data = DEFAULT_PROPS.data,
  total = DEFAULT_PROPS.total,
  flag = DEFAULT_PROPS.flag,
  offset = DEFAULT_PROPS.offset,
  currentPage = DEFAULT_PROPS.currentPage,
  searchInfor = DEFAULT_PROPS.searchInfor,
  limit = DEFAULT_PROPS.limit,
}: PropertiesTableServerProps) => {
  // Utility function for truncating text
  const truncate = (str: string, length: number) => {
    return str.length > length ? str.substring(0, length) + '...' : str;
  };

  // Prepare static table structure
  const tableHeader = (
    <thead className="bg-gray-50">
      <tr>
        {DEFAULT_COLUMNS.map((column) => (
          <th
            key={column.key}
            className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${column.width}`}
          >
            {column.key === 'checkbox' ? (
              <span className="sr-only">Select</span>
            ) : column.key === 'actions' ? (
              <span className="sr-only">Actions</span>
            ) : (
              column.label
            )}
          </th>
        ))}
      </tr>
    </thead>
  );

  const tableRows = data.map((property) => (
    <tr key={property.listing_id} className="hover:bg-gray-50">
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="w-4 h-4 border border-gray-300 rounded"></div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {property.listing_id}
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm text-gray-900">
          {truncate(property.address || 'N/A', 40)}
        </div>
        {property.area && (
          <div className="text-xs text-gray-500">{property.area.name}</div>
        )}
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm text-gray-900">
          {property.homeowner?.name || 'N/A'}
        </div>
        {property.homeowner?.email1 && (
          <div className="text-xs text-gray-500">
            {property.homeowner.email1}
          </div>
        )}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {property.nr_listing_id || 'N/A'}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {property.key_number || 'N/A'}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {property.bedroom_number}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {property.capacity}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        ${property.min_price?.toLocaleString() || 'N/A'}
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <span
          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            property.publish
              ? 'bg-green-100 text-green-800'
              : 'bg-red-100 text-red-800'
          }`}
        >
          {property.publish ? 'Published' : 'Draft'}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        <div className="flex space-x-2">
          <span className="text-blue-600 hover:text-blue-900 cursor-pointer">
            View
          </span>
          <span className="text-blue-600 hover:text-blue-900 cursor-pointer">
            Edit
          </span>
        </div>
      </td>
    </tr>
  ));

  const staticTable = (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        {tableHeader}
        <tbody className="bg-white divide-y divide-gray-200">{tableRows}</tbody>
      </table>
    </div>
  );

  // Pass data and static elements to client component
  return (
    <PropertiesTableClient
      data={data}
      total={total}
      flag={flag}
      offset={offset}
      currentPage={currentPage}
      searchInfor={searchInfor}
      limit={limit}
      staticTable={staticTable}
      columns={DEFAULT_COLUMNS}
    />
  );
};
