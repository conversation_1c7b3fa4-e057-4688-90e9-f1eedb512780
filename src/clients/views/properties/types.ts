import { Property } from '@/types/property';

export type SortConfig = {
  key: string | null;
  direction: 'asc' | 'desc';
};

export type Column = {
  key: string;
  label: string;
  sortable: boolean;
  width: string;
};

export type PropertiesTableBaseProps = {
  data: Property[];
  total?: number;
  flag?: boolean;
  offset?: number;
  currentPage?: number;
  searchInfor?: Record<string, any>;
  limit?: number;
  onPagesChanged?: (page: number) => void;
  onSort?: (sortConfig: { prop: string; order: string }) => void;
  onSubset?: (items: Property[]) => void;
  onShowAllPage?: () => void;
  onLoad?: () => void;
};

export const DEFAULT_COLUMNS: Column[] = [
  { key: 'checkbox', label: '', sortable: false, width: 'w-12' },
  { key: 'listing_id', label: 'ID', sortable: true, width: 'w-20' },
  { key: 'address', label: 'Address', sortable: true, width: 'w-64' },
  { key: 'homeowner', label: 'Owner', sortable: true, width: 'w-32' },
  {
    key: 'nr_listing_id',
    label: 'NR Listing ID',
    sortable: true,
    width: 'w-32',
  },
  { key: 'key_number', label: 'Key #', sortable: true, width: 'w-24' },
  { key: 'bedroom_number', label: 'Beds', sortable: true, width: 'w-20' },
  { key: 'capacity', label: 'Capacity', sortable: true, width: 'w-24' },
  { key: 'min_price', label: 'Min Price', sortable: true, width: 'w-24' },
  { key: 'publish', label: 'Status', sortable: true, width: 'w-24' },
  { key: 'actions', label: 'Actions', sortable: false, width: 'w-32' },
];

export const DEFAULT_PROPS = {
  data: [],
  total: 0,
  flag: false,
  offset: 0,
  currentPage: 1,
  searchInfor: {},
  limit: 25,
};
