/**
 * Example usage of the separated Properties Table components
 * These examples demonstrate different ways to use the components
 */

import React from 'react';
import { Property } from '@/types/property';

// Import the components
import PropertiesTable from './PropertiesTable';
import { PropertiesTableServer } from './PropertiesTableServer';
import { PropertiesTableClient } from './PropertiesTableClient';
import { DEFAULT_COLUMNS } from './types';

// Mock data for examples
const mockProperties: Property[] = [
  {
    listing_id: 1,
    nr_listing_id: 101,
    address: '123 Ocean View Drive',
    area: { id: 1, name: 'Beachfront' },
    homeowner: {
      contact_id: 1,
      name: '<PERSON>',
      cell_phone: '555-0123',
      email1: '<EMAIL>',
    },
    key_number: 'A123',
    bedroom_number: 3,
    capacity: 6,
    min_price: 250,
    publish: true,
    // ... other required Property fields would be here
  } as Property,
  // Add more mock properties as needed
];

/**
 * Example 1: Backward Compatible Usage
 * This is how existing code can continue to work without changes
 */
export const BackwardCompatibleExample = () => {
  const handlePageChange = (page: number) => {
    console.log('Page changed to:', page);
  };

  const handleSort = (sortConfig: { prop: string; order: string }) => {
    console.log('Sort changed:', sortConfig);
  };

  const handleSubset = (items: Property[]) => {
    console.log('Subset created with:', items.length, 'items');
  };

  return (
    <PropertiesTable
      data={mockProperties}
      total={100}
      currentPage={1}
      limit={25}
      onPagesChanged={handlePageChange}
      onSort={handleSort}
      onSubset={handleSubset}
    />
  );
};

/**
 * Example 2: Server Component Only (Static Display)
 * Use this for static displays where no interactivity is needed
 */
export const ServerOnlyExample = () => {
  return (
    <PropertiesTableServer
      data={mockProperties}
      total={100}
      currentPage={1}
      limit={25}
    />
  );
};

/**
 * Example 3: Client Component with Custom Static Table
 * Use this when you want full control over the static table structure
 */
export const ClientWithCustomStaticExample = () => {
  const customStaticTable = (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Custom Header
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {mockProperties.map((property) => (
            <tr key={property.listing_id}>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {property.address}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  const handlePageChange = (page: number) => {
    console.log('Page changed to:', page);
  };

  return (
    <PropertiesTableClient
      data={mockProperties}
      total={100}
      currentPage={1}
      limit={25}
      staticTable={customStaticTable}
      columns={DEFAULT_COLUMNS}
      onPagesChanged={handlePageChange}
    />
  );
};

/**
 * Example 4: Next.js App Router Usage
 * This shows how to use the components in a Next.js app with server/client separation
 */

// Server Component (app/properties/page.tsx)
export const PropertiesPageServer = async () => {
  // This would typically fetch data from an API or database
  const properties = mockProperties;
  const total = 100;

  return (
    <div>
      <h1>Properties</h1>
      <PropertiesTableServer
        data={properties}
        total={total}
        currentPage={1}
        limit={25}
      />
    </div>
  );
};

// Client Component (app/properties/interactive-table.tsx)
export const InteractivePropertiesTable = ({
  initialData,
  initialTotal,
}: {
  initialData: Property[];
  initialTotal: number;
}) => {
  const [data, setData] = React.useState(initialData);
  const [total, setTotal] = React.useState(initialTotal);
  const [currentPage, setCurrentPage] = React.useState(1);

  const handlePageChange = async (page: number) => {
    // Fetch new data for the page
    // const newData = await fetchProperties(page);
    // setData(newData.properties);
    // setTotal(newData.total);
    setCurrentPage(page);
  };

  const handleSort = async (sortConfig: { prop: string; order: string }) => {
    // Fetch sorted data
    // const sortedData = await fetchProperties(currentPage, sortConfig);
    // setData(sortedData.properties);
    console.log('Sort:', sortConfig);
  };

  return (
    <PropertiesTableClient
      data={data}
      total={total}
      currentPage={currentPage}
      limit={25}
      staticTable={<div />} // Placeholder since we're handling everything client-side
      columns={DEFAULT_COLUMNS}
      onPagesChanged={handlePageChange}
      onSort={handleSort}
    />
  );
};
