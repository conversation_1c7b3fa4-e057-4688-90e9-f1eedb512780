'use client';

import Button from '@/clients/ui/button';
import Checkbox from '@/clients/ui/checkbox';
import { useCallback, useState } from 'react';

type Props = {};

const AmenitiesFilter = () => {
  const [showPopup, setShowPopup] = useState<boolean>(false);
  const [state, setState] = useState({
    air_conditioning_central: false,
    air_conditioning_minisplit: false,
    air_conditioning_windowunits: false,
    pets_askowner: false,
    pets_yes: false,
    pets_no: false,
    pool_private: false,
    pool_community: false,
    walk_to_beach: false,
    waterfront: false,
    water_views: false,
  });

  const onTogglePopup = useCallback(() => {
    setShowPopup((_s) => !_s);
  }, []);

  const onChangeCheckbox = useCallback()

  return (
    <>
      <div
        className="p-2 border rounded w-full cursor-pointer relative"
        onClick={onTogglePopup}
      >
        <p className="text-sm">Amenities & Features</p>
        {showPopup && (
          <div className="absolute left-0 right-0 min-w-full top-[40px] bg-white shadow-card py-2 rounded-md z-[999] p-2">
            <div className="flex items-center gap-x-2 my-2">
              <Checkbox className="w-4 h-4" />
              <label className="text-sm">A/C: Central</label>
            </div>
            <div className="flex items-center gap-x-2 my-2">
              <Checkbox className="w-4 h-4" />
              <label className="text-sm">A/C: Mini Split</label>
            </div>
            <div className="flex items-center gap-x-2 my-2">
              <Checkbox className="w-4 h-4" />
              <label className="text-sm">A/C: Window Units</label>
            </div>
            <div className="flex items-center gap-x-2 my-2">
              <Checkbox className="w-4 h-4" />
              <label className="text-sm">Pets: Yes</label>
            </div>
            <div className="flex items-center gap-x-2 my-2">
              <Checkbox className="w-4 h-4" />
              <label className="text-sm">Pets: No</label>
            </div>
            <div className="flex items-center gap-x-2 my-2">
              <Checkbox className="w-4 h-4" />
              <label className="text-sm">Pets: Ask Owner</label>
            </div>
            <div className="flex items-center gap-x-2 my-2">
              <Checkbox className="w-4 h-4" />
              <label className="text-sm">Pool: Private</label>
            </div>
            <div className="flex items-center gap-x-2 my-2">
              <Checkbox className="w-4 h-4" />
              <label className="text-sm">Pool: Community</label>
            </div>
            <div className="flex items-center gap-x-2 my-2">
              <Checkbox className="w-4 h-4" />
              <label className="text-sm">Walk to the beach</label>
            </div>
            <div className="flex items-center gap-x-2 my-2">
              <Checkbox className="w-4 h-4" />
              <label className="text-sm">Waterfront</label>
            </div>
            <div className="flex items-center gap-x-2 my-2">
              <Checkbox className="w-4 h-4" />
              <label className="text-sm">Water views</label>
            </div>
            <Button className="w-full text-sm">Done</Button>
          </div>
        )}
      </div>
      {showPopup && (
        <div
          className="drawer-overlay absolute inset-0 z-[99]"
          onClick={onTogglePopup}
        />
      )}
    </>
  );
};

export default AmenitiesFilter;
